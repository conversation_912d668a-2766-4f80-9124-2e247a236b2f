# EverQuest Map Tracker

A Python application that hooks into EverQuest to track zones, create visual maps, and show real-time mob locations with names and levels.

## Features

- Tracks your current zone in EverQuest
- Creates a visual map of the zone
- Shows real-time mob locations with names and levels in the format "MobName(Level)"
- Updates automatically while you play

## Requirements

- Windows operating system
- Python 3.7 or higher
- EverQuest game client

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. Start EverQuest and log in with your character
2. Run the application:

```bash
python eq_map_tracker.py
```

3. Click the "Connect to EQ" button to connect to the game
4. The map will display your current location and any mobs in the vicinity
5. Mob names and levels will be displayed in the format "MobName(Level)"

## How It Works

The application reads the memory of the EverQuest process to obtain information about:
- The current zone
- Your character's position
- Positions, names, and levels of mobs in the zone

This information is then displayed on a map interface that updates in real-time.

## Limitations

- The current implementation uses placeholder values for memory addresses. These would need to be updated based on your specific EverQuest client version.
- The application requires read access to the EverQuest process memory, which may be blocked by some anti-cheat systems.
- Zone maps are currently generated as simple grids. More detailed maps would require additional data.

## Customization

You can modify the following aspects of the application:
- Update interval: Change the `update_interval` value in the `Application` class
- Display colors: Modify the color values in the `draw_player` and `draw_entity` methods
- Map scale: Adjust the `scale` property of the `MapRenderer` class

## Next Steps for Development

To make this application fully functional, you would need to:

1. Identify the correct memory addresses for your EverQuest client version
2. Update the placeholder functions in the `MemoryReader` class with actual memory reading code
3. Add more detailed zone maps if desired
4. Implement additional features like filtering mob types, search functionality, etc.

## Disclaimer

This tool is for educational purposes only. Use at your own risk. The authors are not responsible for any consequences that may arise from using this application, including but not limited to account suspension or banning.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
